/*
 * TKE Copyright (C) by 2014 THL A29 Limited, a Tencent company
 *
 * Tencent Kubernetes Engine(TKE) is licensed under a
 * Creative Commons Attribution-NonCommercial-NoDerivatives 4.0 International License.
 *
 * You should have received a copy of the license along with this
 * work. If not, see <http://creativecommons.org/licenses/by-nc-nd/4.0/>.
 */

package monitor

import (
	"fmt"
	"strconv"
	"sync"
	"time"

	"git.woa.com/tke-library/log"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/util/errors"
	v1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/tke/tke-monitor/core/model"
	"git.woa.com/tke/tke-monitor/core/store"
	"git.woa.com/tke/tke-monitor/metrics"
	"git.woa.com/tke/tke-monitor/pkg/cloudapi/platform"
	"git.woa.com/tke/tke-monitor/pkg/cloudapi/xingyun"
	"git.woa.com/tke/tke-monitor/pkg/cloudapi/zhiyan"
	"git.woa.com/tke/tke-monitor/pkg/k8s"
	"git.woa.com/tke/tke-monitor/pkg/node"
	"git.woa.com/tke/tke-monitor/pkg/util"
)

func ClusterHealthCheck(from, product, object string, singleTimeout, interval time.Duration, totalCount uint32, clusterID string) (avgDelay, maxDelay time.Duration, results []string, successCount, failedCount uint32, err error) {
	log.WithField("from", from).WithField("product", product).WithField("object", object).WithField("cluster_id", clusterID).Info("dial to apiserver")
	var platformClient platform.PlatformClient
	var clusterType = product

	switch {
	case product == "tke" && from == "grpc":
		platformClient = *platform.GrpcTKEClient
	case product == "tke" && from == "cron":
		platformClient = *platform.TKEClient
	case product == "eks" && from == "grpc":
		platformClient = *platform.GrpcEKSClient
	case product == "eks" && from == "cron":
		platformClient = *platform.EKSClient
	default:
		return avgDelay, maxDelay, results, successCount, failedCount, fmt.Errorf("invalid cluster product %s", product)
	}

	cluster, err := platformClient.GetCluster(clusterID)
	if err != nil {
		return avgDelay, maxDelay, results, successCount, failedCount, fmt.Errorf("failed to get platform cluser: %v", err)
	}
	if cluster.Spec.Type == "tke" {
		clusterType = fmt.Sprintf("%s.%s", product, cluster.Annotations["tke.cloud.tencent.com/clusterType"])
	}
	clientSet, err := platformClient.ClientSetFor(cluster)
	if err != nil {
		return avgDelay, maxDelay, results, successCount, failedCount, err
	}

	var errorCount uint32
	var successElapsedTime time.Duration
	var endpoint string
	for _, addr := range cluster.Status.Addresses {
		if addr.Type == "Internal" {
			endpoint = fmt.Sprintf("https://%s:%d%s", addr.Host, addr.Port, addr.Path)
		}
	}
	appID, err := strconv.ParseUint(cluster.Spec.TenantID, 10, 64)
	if err != nil {
		return avgDelay, maxDelay, results, successCount, failedCount, fmt.Errorf("failed to parse appID %s", cluster.Spec.TenantID)
	}
	uin, isBigCustomer, username := util.GetCustomerInfo(appID)
	var errs []error
	for i := uint32(0); i < totalCount; i++ {
		elapsedTime, err := k8s.IsClusterAlive(object, clientSet)
		if err == nil {
			successCount++
			successElapsedTime += elapsedTime
			if elapsedTime > maxDelay {
				maxDelay = elapsedTime
			}
			metrics.RecordDialTestDuration(cluster.Name, object, clusterType, fmt.Sprint(appID), from, elapsedTime)
			results = append(results, elapsedTime.String())
		} else {
			failedCount++
			errs = append(errs, err)
			results = append(results, err.Error())
			log.WithError(err).WithField("cluster", cluster.Name).WithField("object", object).Error("failed to dial test")
			errorCount++
			if object == k8s.EtcdObject {
				etcdName := store.GetEtcdName(cluster.Name)
				metrics.ReportFailedToDialTestEtcd(etcdName, cluster.Name, clusterType, from)
			}
			metrics.RecordDialTestFailed(cluster.Name, object, clusterType, fmt.Sprint(appID), from, elapsedTime)
			time.Sleep(time.Duration(errorCount*3) * interval)
		}
		time.Sleep(interval)
	}
	if successElapsedTime > time.Second {
		log.WithField("successElapsedTime", successElapsedTime).WithField("successCount", successCount).WithField("clusterID", cluster.Name).WithField("object", object).Info("long dial test")
	}
	errInfo := ""
	if len(errs) > 0 {
		errInfo = errors.NewAggregate(errs).Error()
		if len(errInfo) > 1024 {
			errInfo = errInfo[:1024]
		}
	}
	if from == "cron" {
		if successCount < totalCount {
			var exist bool
			if exist, err = checkClusterExist(product, cluster.Name); err != nil {
				log.Errorf("failed to check cluster exist: %v", err)
			} else if !exist {
				log.WithField("clusterID", cluster.Name).WithField("object", object).Info("cluster is terminating")
				return
			}
			if successCount == 0 {
				// 一次都无法连接
				// nolint: errcheck
				zhiyan.SendClusterAPIServerTimeoutAlarm("全部失败", object, cluster.Name, cluster.Spec.DisplayName, appID, uin, isBigCustomer, clusterType, username, endpoint,
					fmt.Sprintf("连续%d次拨测一次都未连接成功", totalCount), errInfo)
			} else if successCount < totalCount-1 { // 降低告警敏感度
				avgDelay = time.Duration(int64(successElapsedTime) / int64(successCount))
				// 中间有次数连接失败
				// nolint: errcheck
				zhiyan.SendClusterAPIServerTimeoutAlarm("部分失败", object, cluster.Name, cluster.Spec.DisplayName, appID, uin, isBigCustomer, clusterType, username, endpoint,
					fmt.Sprintf("拨测%d次仅成功%d次", totalCount, successCount), errInfo)
			} else {
				// 只失败一次，就再试一次
				if _, err := k8s.IsClusterAlive(object, clientSet); err != nil {
					zhiyan.SendClusterAPIServerTimeoutAlarm("部分失败", object, cluster.Name, cluster.Spec.DisplayName, appID, uin, isBigCustomer, clusterType, username, endpoint,
						fmt.Sprintf("拨测%d次仅成功%d次", totalCount+1, successCount), errInfo)
				}
				log.WithField("clusterID", cluster.Name).WithField("object", object).Warn("only 1 dial failed, retry once success")
			}
		} else if successElapsedTime > singleTimeout*time.Duration(successCount) {
			// 成功的平均延时超过了apiserverElapsedTime(ms)
			// nolint: errcheck
			zhiyan.SendClusterAPIServerTimeoutAlarm("连接慢", object, cluster.Name, cluster.Spec.DisplayName, appID, uin, isBigCustomer, clusterType, username, endpoint,
				fmt.Sprintf("拨测成功%d次平均时延超过%s毫秒(实际平均用时：%s)", successCount, singleTimeout.String(), avgDelay.String()), errInfo)
		}
	}
	return
}

func checkClusterExist(product string, clusterID string) (bool, error) {
	switch product {
	case "tke":
		clusterInfo, err := store.GetClusterByClusterInstanceID(clusterID)
		if err != nil {
			return false, err
		}
		if clusterInfo == nil || clusterInfo.LifeState == model.CLUSTER_LIFESTATE_DELETING {
			return false, nil
		}
	case "eks":
		if cluster, err := platform.EKSClient.GetCluster(clusterID); err != nil {
			if apierrors.IsNotFound(err) {
				return false, nil
			}
			return false, err
		} else if cluster.Status.Phase == v1.ClusterTerminating {
			return false, nil
		}
	default:
		return false, fmt.Errorf("invalid cluster product %s", product)
	}

	return true, nil
}
