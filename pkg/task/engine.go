package task

import (
	"context"
	"errors"
	"fmt"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/infra"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	pkgutil "git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

// 任务执行引擎，完成任务的预检、执行、后检。
// 1) 重载操作。针对有自定义预检、后检的任务，执行各个组件自定义的操作
// 2）所有的任务都不进行重试。针对预检和后检任务，不做重试操作，直接返回错误
// 3）支持变更类型：k8snative、mastercrd、plugin、addon
// 4) 任务只有3个状态：pending -》 processing -》done。任务执行出错看Reason，Reason不为空则任务执行出错。

// 处理单个任务,处理顺序： precheck -> upgrade -> postcheck
// TODO: 任务组件重启后，没执行完的任务怎么办？
func processTask(parentTask *infra.StarshipTask) error {
	// 准备阶段：准备任务调用参数
	if parentTask == nil {
		return fmt.Errorf("parent task is empty")
	}
	baseLog := fmt.Sprintf("traceId:%s", parentTask.TraceId)
	taskStatus := &TaskStatus{}

	defer func() {
		if taskStatus.Status == "" {
			return
		}
		// defer处理parent task的任务状态。各个子任务的状态在子函数 precheck、upgrade、postcheck 中处理
		parentTask.Phase = taskStatus.Phase
		// 所有任务完成时，标识父任务的状态，任务继承Reason信息
		parentTask.Status = taskStatus.Status
		parentTask.Reason = taskStatus.Reason
		klog.Infof("update parent task, phase:%s, status:%s, %s", parentTask.Phase, parentTask.Status, baseLog)
		err := infra.UpdateTask(parentTask)
		if err != nil {
			klog.Errorf("update parent task status failed, err:%v, %s", err, baseLog)
		}
	}()

	subTasks, err := infra.GetSubTaskByParent(parentTask.ID, "")
	if err != nil {
		taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, err.Error())
		return err
	} else if len(subTasks) == 0 {
		klog.Infof("no need run upgrade, %s", baseLog)
		return nil
	}
	// 构建子任务map，对于后检任务需要按照StartTime排序
	subTaskMap := make(map[string][]*infra.StarshipSubtask)
	for _, v := range subTasks {
		subTaskMap[v.Action] = append(subTaskMap[v.Action], v)
	}

	ctx, req, err := buildCommonParam(parentTask)
	if err != nil {
		klog.Errorf("build strategy provider param failed: %v, %s", err, baseLog)
		taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, err.Error())
		return err
	}

	// 判断集群是否存在，不存在直接将任务设置为done，并且写入错误信息
	clusterExists, err := kubeclient.IsClusterExist(req.ClusterId, req.ProductName)
	if err != nil {
		taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, err.Error())
		klog.Errorf("check cluster existence failed, err:%v, %s", err, baseLog)
		return err
	}
	if !clusterExists {
		errMsg := fmt.Sprintf("cluster %s does not exist", req.ClusterId)
		taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, errMsg)
		klog.Errorf("%s, %s", errMsg, baseLog)
		return errors.New(errMsg)
	}

	// 过期判断，如果任务过期，则设置任务状态为已完成，不要每次都进入循环处理
	if flag, phase := isExpiredTask(parentTask, subTasks); flag {
		taskStatus = setTaskStatus(phase, pkgutil.TaskStatusDone, "error: task expired")
		return nil
	}
	// 1. 预检
	// 判断是否需要执行预检
	if needPrecheck(subTaskMap[pkgutil.TaskActionPreCheck]) {
		klog.Infof("run precheck, %s", baseLog)
		precheckTask := subTaskMap[pkgutil.TaskActionPreCheck][0]
		err = precheck(precheckTask, ctx, req)
		if err != nil {
			taskStatus = setTaskStatus(pkgutil.TaskActionPreCheck, pkgutil.TaskStatusDone, err.Error())
			klog.Errorf("precheck failed, err:%v, %s", err, baseLog)
			return err
		}
		// plugin/grpc 方式, 等异步上报执行结果
		if pkgutil.IsAsyncStrategy(req.PreCheckStrategy) {
			taskStatus = setTaskStatus(pkgutil.TaskActionPreCheck, pkgutil.TaskStatusProcessing, "")
			return nil
		}
		// 只有一个阶段时，执行完就上报
		if len(subTaskMap) == 1 {
			taskStatus = setTaskStatus(pkgutil.TaskActionPreCheck, pkgutil.TaskStatusDone, "")
		}
	}

	// 2. 执行
	// 判断是否需要升级
	if needUpgrade(req.PreCheckStrategy, subTaskMap) {
		klog.Infof("run upgrade, %s", baseLog)
		upgradeTask := subTaskMap[pkgutil.TaskActionUpgrade][0]
		err = update(upgradeTask, req.Strategy, ctx, req)
		if err != nil {
			taskStatus = setTaskStatus(pkgutil.TaskActionUpgrade, pkgutil.TaskStatusDone, err.Error())
			klog.Errorf("upgrade failed, err:%v, %s", err, baseLog)
			return err
		}
		// NOTICE: 对于plugin/grpc/eklet-agent任务，是异步执行的
		// 对于ds任务，需要根据子任务状态判断是否为异步
		if pkgutil.IsAsyncStrategy(req.Strategy) ||
			req.Strategy == pkgutil.ReleaseStrategyEkletagent ||
			req.Strategy == pkgutil.ReleaseStrategyAddon ||
			(req.Strategy == pkgutil.ReleaseStrategyDaemonSet && upgradeTask.Status == pkgutil.TaskStatusProcessing) {
			taskStatus = setTaskStatus(pkgutil.TaskActionUpgrade, pkgutil.TaskStatusProcessing, "")
			return nil
		}
		// 只有一个阶段时，执行完就上报
		if len(subTaskMap) == 1 {
			taskStatus = setTaskStatus(pkgutil.TaskActionUpgrade, pkgutil.TaskStatusDone, "")
		}
	}

	// 3. 回滚
	// 判断是否需要升级
	if needRollback(req.Strategy, subTaskMap) {
		klog.Infof("run rollback, %s", baseLog)
		rollbackTask := subTaskMap[pkgutil.TaskActionRollback][0]
		err = update(rollbackTask, req.Strategy, ctx, req)
		if err != nil {
			taskStatus = setTaskStatus(pkgutil.TaskActionRollback, pkgutil.TaskStatusDone, err.Error())
			klog.Errorf("rollback failed, err:%v, %s", err, baseLog)
			return err
		}
		// NOTICE: 对于plugin/grpc/eklet-agent任务，是异步执行的
		if pkgutil.IsAsyncStrategy(req.Strategy) || req.Strategy == pkgutil.ReleaseStrategyAddon {
			taskStatus = setTaskStatus(pkgutil.TaskActionRollback, pkgutil.TaskStatusProcessing, "")
			return nil
		}
		// 只有一个阶段时，执行完就上报
		if len(subTaskMap) == 1 {
			taskStatus = setTaskStatus(pkgutil.TaskActionRollback, pkgutil.TaskStatusDone, "")
		}
	}

	// 4. 后检
	// 判断是否需要执行后检
	if needPostcheck(req.Strategy, subTaskMap) {
		klog.Infof("run postcheck, %s", baseLog)
		// 遍历所有待执行的后检任务，找到第一个未完成且在时间窗口内的任务执行
		hasRemainingTask := false
		for _, postcheckTask := range subTaskMap[pkgutil.TaskActionPostCheck] {
			// 跳过已完成的任务
			if postcheckTask.Status == pkgutil.TaskStatusDone {
				continue
			}
			// 执行后检任务
			err = postcheck(postcheckTask, ctx, req)
			if err != nil {
				taskStatus = setTaskStatus(pkgutil.TaskActionPostCheck, pkgutil.TaskStatusDone, err.Error())
				klog.Errorf("postcheck failed, err:%v, %s", err, baseLog)
				return err
			}
			for _, remainingTask := range subTaskMap[pkgutil.TaskActionPostCheck] {
				if remainingTask.Status != pkgutil.TaskStatusDone && time.Now().Before(remainingTask.StartTime) {
					hasRemainingTask = true
					break
				}
			}
			break // 每次只执行一个后检任务
		}
		// plugin/grpc 异步更新
		if pkgutil.IsAsyncStrategy(req.PostCheckStrategy) {
			taskStatus = setTaskStatus(pkgutil.TaskActionPostCheck, pkgutil.TaskStatusProcessing, "")
			return nil
		}
		// 如果还有未执行的后检任务，设置状态为processing
		if hasRemainingTask {
			taskStatus = setTaskStatus(pkgutil.TaskActionPostCheck, pkgutil.TaskStatusProcessing, "")
			return nil
		}
		// 所有后检任务都执行完成，设置状态为done
		taskStatus = setTaskStatus(pkgutil.TaskActionPostCheck, pkgutil.TaskStatusDone, "")
	}
	return nil
}

// 执行预检任务
func precheck(subTask *infra.StarshipSubtask, ctx *strategyprovider.ReleaseConfig, req *strategyprovider.TaskStrategyRequest) error {
	baseLog := fmt.Sprintf("task-id:%d, subtask-id:%d, traceId:%s", subTask.ParentTaskId, subTask.ID, req.TraceId)
	ctx, req = buildStrategyProviderParam(ctx, req, subTask)

	taskStatus := &TaskStatus{}
	defer func() {
		if taskStatus.Status == "" {
			return
		}
		// 对于plugin/grpc/eklet-agent任务，由任务执行完成后更新状态，这里不更新状态
		if pkgutil.IsAsyncStrategy(req.PreCheckStrategy) && taskStatus.Status == pkgutil.TaskStatusDone && taskStatus.Reason == "" {
			klog.Infof("async task is done, wait for async to report precheck status, %s", baseLog)
			taskStatus.Status = pkgutil.TaskStatusProcessing
		}

		klog.Infof("update subtask status to %s, %s", taskStatus.Status, baseLog)
		subTask.Status = taskStatus.Status
		if subTask.Status == pkgutil.TaskStatusDone {
			now := time.Now()
			subTask.EndTime = &now
		}
		subTask.Reason = taskStatus.Reason
		err := infra.UpdateSubTask(subTask)
		if err != nil {
			klog.Errorf("update subtask status failed, err:%v, %s", err, baseLog)
		}
	}()

	strategy, err := strategyprovider.GetReleaseStrategyProvider(pkgutil.GenericStrategyPrefix+pkgutil.TaskActionPreCheck, ctx)
	if err != nil {
		klog.Errorf("get release strategy provider failed: %v, %s", err, baseLog)
		taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, err.Error())
		return err
	}
	if _, err := strategy.Exec(context.TODO(), req); err != nil {

	// 设置合理的超时时间，避免context deadline exceeded
	checkCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if _, err := strategy.Exec(checkCtx, req); err != nil {
		klog.Errorf("exec precheck err:%v, %s", err, baseLog)
		taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, err.Error())
		return err
	}
	// 查询预检查任务没有风险，则修改父任务为完成状态
	hasRisk, err := isHealthCheckHitRisk(subTask.ID)
	if err != nil {
		klog.Errorf("query risks failed: %v, %s", err, baseLog)
		taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, err.Error())
		return err
	}
	if hasRisk {
		errMsg := "error: pre-check hit risks"
		taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, errMsg)
		return fmt.Errorf("%s", errMsg)
	}

	taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, "")
	return nil
}

// upgrade or rollback
func update(subTask *infra.StarshipSubtask, releaseStrategy string, ctx *strategyprovider.ReleaseConfig, req *strategyprovider.TaskStrategyRequest) error {
	baseLog := fmt.Sprintf("task-id:%d, subtask-id:%d, traceId:%s", subTask.ParentTaskId, subTask.ID, req.TraceId)
	ctx, req = buildStrategyProviderParam(ctx, req, subTask)

	taskStatus := &TaskStatus{}
	defer func() {
		if taskStatus.Status == "" {
			return
		}
		klog.Infof("update subtask status, %s", baseLog)
		subTask.Status = taskStatus.Status
		if subTask.Status == pkgutil.TaskStatusDone {
			now := time.Now()
			subTask.EndTime = &now
		}
		subTask.Reason = taskStatus.Reason
		err := infra.UpdateSubTask(subTask)
		if err != nil {
			klog.Errorf("update parent task status failed, err:%v, %s", err, baseLog)
		}
	}()

	// 对于addon任务，upgrade不用执行, 即没有exec阶段的调用。等待调用report接口将upgrade任务置为done即可
	if releaseStrategy == pkgutil.ReleaseStrategyAddon {
		klog.Infof("addon task, wait for application-controller to update the task to done, skip upgrade, %d", subTask.ID)
		if subTask.Status != pkgutil.TaskStatusProcessing {
			taskStatus = setTaskStatus("", pkgutil.TaskStatusProcessing, "")
		}
		return nil
	}

	strategy, err := strategyprovider.GetReleaseStrategyProvider(releaseStrategy, ctx)
	if err != nil {
		klog.Errorf("get release strategy provider failed: %v, %s", err, baseLog)
		taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, err.Error())
		return err
	}
	klog.V(5).Infof("task strategy request is %v", req)

	var reply *strategyprovider.TaskStrategyReply
	if subTask.Action == pkgutil.TaskActionUpgrade {
		if reply, err = strategy.Exec(context.TODO(), req); err != nil {
			taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, err.Error())
			return err
		}
	} else {
		// TODO: rollback暂不考虑ds回滚场景
		if _, err := strategy.Rollback(context.TODO(), req); err != nil {
			taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, err.Error())
			return err
		}
	}
	// NOTICE: 对于plugin/grpc/eklet-agent任务，是异步执行的
	// 对于ds的ondelete模式是异步执行的
	if pkgutil.IsAsyncStrategy(req.Strategy) ||
		(req.Strategy == pkgutil.ReleaseStrategyEkletagent) ||
		(req.Strategy == pkgutil.ReleaseStrategyDaemonSet && reply != nil &&
			reply.Detail == string(appsv1.OnDeleteDaemonSetStrategyType)) {
		taskStatus = setTaskStatus("", pkgutil.TaskStatusProcessing, "")
		return nil
	}
	taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, "")
	return nil
}

// 执行后检任务
func postcheck(subTask *infra.StarshipSubtask, ctx *strategyprovider.ReleaseConfig, req *strategyprovider.TaskStrategyRequest) error {
	if subTask == nil {
		return nil
	}
	baseLog := fmt.Sprintf("task-id:%d, subtask-id:%d, traceId:%s", subTask.ParentTaskId, subTask.ID, req.TraceId)
	ctx, req = buildStrategyProviderParam(ctx, req, subTask)

	taskStatus := &TaskStatus{}
	defer func() {
		if taskStatus.Status == "" {
			return
		}

		// 对于plugin/grpc任务，由任务执行完成后更新状态，这里不更新状态
		if pkgutil.IsAsyncStrategy(req.PostCheckStrategy) && taskStatus.Status == pkgutil.TaskStatusDone && taskStatus.Reason == "" {
			klog.Infof("async task is done, wait for async to report precheck status, %s", baseLog)
			taskStatus.Status = pkgutil.TaskStatusProcessing
		}

		klog.Infof("update subtask status, %s", baseLog)
		subTask.Status = taskStatus.Status
		if subTask.Status == pkgutil.TaskStatusDone {
			now := time.Now()
			subTask.EndTime = &now
		}
		subTask.Reason = taskStatus.Reason
		err := infra.UpdateSubTask(subTask)
		if err != nil {
			klog.Errorf("update parent task status failed, err:%v, %s", err, baseLog)
		}
	}()

	strategy, err := strategyprovider.GetReleaseStrategyProvider(pkgutil.GenericStrategyPrefix+pkgutil.TaskActionPostCheck, ctx)
	if err != nil {
		klog.Errorf("get release strategy provider failed: %v, %s", err, baseLog)
		taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, err.Error())
		return err
	}

	if _, err := strategy.Exec(context.TODO(), req); err != nil {
	// 设置合理的超时时间，避免context deadline exceeded
	checkCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if _, err := strategy.Exec(checkCtx, req); err != nil {
		klog.Errorf("exec postcheck err:%v, %s", err, baseLog)
		taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, err.Error())
		return err
	}

	// 查询后检查任务没有风险，则修改父任务为完成状态
	hasRisk, err := isHealthCheckHitRisk(subTask.ID)
	if err != nil {
		klog.Errorf("query risks failed: %v, %s", err, baseLog)
		taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, err.Error())
		return err
	}
	if hasRisk {
		errMsg := "error: post-check hit risks"
		taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, errMsg)
		return fmt.Errorf("%s", errMsg)
	}
	taskStatus = setTaskStatus("", pkgutil.TaskStatusDone, "")
	return nil
}
